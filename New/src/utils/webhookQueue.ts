/**
 * Webhook Queue Utilities
 *
 * Provides utilities for managing webhook queue operations with duplicate prevention.
 * Implements atomic delete-then-insert operations to prevent race conditions and
 * ensure only the most recent webhook for each sourceId+type combination is processed.
 *
 * @fileoverview Webhook queue management utilities
 * @version 1.0.0
 * @since 2024-08-05
 */

import { dbSchema } from "@database";
import { and, eq } from "drizzle-orm";
import type { NeonHttpDatabase } from "drizzle-orm/neon-http";
import type { APContactCreationWebhookPayload } from "@/processors/apWebhook";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import { logDebug, logInfo } from "@/utils/logger";

/**
 * Type for webhook queue insert operations
 */
type WebhookQueueInsert = typeof dbSchema.webhookQueue.$inferInsert;

/**
 * Type for webhook queue select operations
 */
type WebhookQueue = typeof dbSchema.webhookQueue.$inferSelect;

/**
 * Database type for the webhook queue operations
 */
type DrizzleDb = NeonHttpDatabase<typeof dbSchema>;

/**
 * Add webhook to queue with duplicate prevention
 *
 * This function implements atomic duplicate prevention by:
 * 1. Starting a database transaction
 * 2. Querying for existing pending webhooks with same sourceId and type
 * 3. Deleting any found pending webhooks
 * 4. Inserting the new webhook record
 * 5. Returning the created webhook
 *
 * This ensures that only the most recent webhook for each sourceId+type
 * combination is processed, preventing race conditions and duplicate processing.
 *
 * @param db - Drizzle database instance
 * @param webhookData - Webhook data to insert
 * @returns Promise resolving to the created webhook record
 *
 * @throws {Error} When transaction fails or database operation errors occur
 *
 * @example
 * ```typescript
 * const webhook = await addWebhookToQueue(db, {
 *   source: "cc",
 *   sourceId: "123",
 *   type: "patient",
 *   payload: ccWebhookPayload,
 *   status: "pending"
 * });
 * ```
 *
 * @since 1.0.0
 */
export async function addWebhookToQueue(
	db: DrizzleDb,
	webhookData: WebhookQueueInsert,
): Promise<WebhookQueue> {
	return await db.transaction(async (tx) => {
		// Step 1: Find existing pending webhooks with same sourceId and type
		const existingWebhooks = await tx
			.select({ id: dbSchema.webhookQueue.id })
			.from(dbSchema.webhookQueue)
			.where(
				and(
					eq(dbSchema.webhookQueue.sourceId, webhookData.sourceId),
					eq(
						dbSchema.webhookQueue.type,
						webhookData.type as "patient" | "appointment",
					),
					eq(dbSchema.webhookQueue.status, "pending"),
				),
			);

		// Step 2: Delete existing pending webhooks if found
		if (existingWebhooks.length > 0) {
			const deletedIds = existingWebhooks.map((w) => w.id);

			await tx
				.delete(dbSchema.webhookQueue)
				.where(
					and(
						eq(dbSchema.webhookQueue.sourceId, webhookData.sourceId),
						eq(
							dbSchema.webhookQueue.type,
							webhookData.type as "patient" | "appointment",
						),
						eq(dbSchema.webhookQueue.status, "pending"),
					),
				);

			logInfo(
				`Removed ${existingWebhooks.length} existing pending webhook(s) for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
				{
					deletedWebhookIds: deletedIds,
					sourceId: webhookData.sourceId,
					type: webhookData.type,
					source: webhookData.source,
				},
			);
		}

		// Step 3: Insert the new webhook record
		const [createdWebhook] = await tx
			.insert(dbSchema.webhookQueue)
			.values(webhookData)
			.returning();

		logDebug(
			`Added new webhook to queue: ${createdWebhook.id} (${webhookData.source}:${webhookData.sourceId}:${webhookData.type})`,
			{
				webhookId: createdWebhook.id,
				sourceId: webhookData.sourceId,
				type: webhookData.type,
				source: webhookData.source,
				replacedExisting: existingWebhooks.length > 0,
			},
		);

		return createdWebhook;
	});
}

/**
 * Extract source information from webhook payload
 *
 * Determines the source platform, sourceId, and type from webhook payload.
 * Used to normalize webhook data before queue insertion.
 *
 * @param payload - AP or CC webhook payload
 * @returns Object containing source, sourceId, and type information
 *
 * @throws {Error} When payload is invalid or missing required fields
 *
 * @example
 * ```typescript
 * const sourceInfo = extractSourceInfo(ccWebhookPayload);
 * // Returns: { source: "cc", sourceId: "123", type: "patient" }
 * ```
 *
 * @since 1.0.0
 */
export function extractSourceInfo(
	payload: APContactCreationWebhookPayload | CCWebhookPayload,
): {
	source: "ap" | "cc";
	sourceId: string;
	type: "patient" | "appointment";
} {
	// Check if it's an AP webhook payload
	if ("contact_id" in payload) {
		return {
			source: "ap",
			sourceId: payload.contact_id,
			type: payload.calendar ? "appointment" : "patient",
		};
	}

	// Check if it's a CC webhook payload
	if ("id" in payload) {
		return {
			source: "cc",
			sourceId: payload.id.toString(),
			type:
				payload.model?.toLowerCase() === "appointment"
					? "appointment"
					: "patient",
		};
	}

	throw new Error(
		"Invalid webhook payload: missing required identification fields",
	);
}
