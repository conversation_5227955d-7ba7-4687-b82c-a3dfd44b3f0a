# Prevent Duplicate Processing - Loop Prevention Strategy

## Understanding the Current v3Integration Pattern

After analyzing the v3Integration Skip model and its usage, I understand the exact loop prevention pattern:

### The Problem We're Solving

**Bidirectional Sync Loop:**
1. CC sends webhook: "Patient 123 was updated"
2. We process it → Update patient in AP
3. AP sends webhook: "Contact ABC was updated" (same patient, triggered by our update)
4. We process it → Update patient in CC
5. <PERSON> sends webhook: "Patient 123 was updated" (triggered by our update)
6. **INFINITE LOOP** 🔄

### Current v3Integration Skip Pattern

**How it works:**
1. **Before Processing:** Check if we recently processed this exact operation
   - `Skip.hasProcessPatientUpdate(ccId)` - checks and **deletes** the record if found
   - If found → Skip processing (we did this recently)
   - If not found → Continue processing

2. **After Successful API Call:** Record that we just made this operation
   - `Skip.putProcessPatientUpdate(ccId)` - creates a record
   - This prevents the incoming webhook (caused by our API call) from being processed

**Key Insight:** The Skip record is created **AFTER** we make an API call to the other platform, not before processing the webhook.

### Current Skip Model Structure

```typescript
// v3Integration/app/Models/Skip.ts
{
  id: number,
  action: string,           // "ProcessPatientUpdate", "ProcessPatientCreate", etc.
  ccId: number,            // CliniCore patient ID
  apId: string,            // AutoPatient contact ID
  createdAt: DateTime,
  updatedAt: DateTime
}
```

**Actions tracked:**
- `ProcessPatientUpdate` (with ccId)
- `putProcessPatientCreate` (with apId) 
- `ProcessAppointmentUpdate` (with ccId)
- `ProcessAppointmentCreate` (with apId)

### The "Check and Delete" Pattern

**Critical Pattern:** `hasProcessPatientUpdate()` does:
1. Query for existing record
2. If found → **Delete it** and return `true`
3. If not found → return `false`

This is a **one-time use** pattern - each skip record can only prevent one duplicate.

## Current New System Issues

The new system I implemented has these problems:
1. **Wrong timing** - I was checking before processing instead of after API calls
2. **Persistent records** - Records don't get deleted after use
3. **Over-engineered** - Complex hashing when simple ID-based tracking works
4. **Missing the point** - Not understanding the exact moment when skip records should be created

## Correct Implementation Plan

### 1. Simple Skip Model for New System

Use the existing `skip` table but with proper methods:

```sql
-- Already exists in New/src/database/schema.ts
skip {
  id: varchar(255) PRIMARY KEY,
  created_at: timestamp,
  updated_at: timestamp,
  patient_id: varchar(255),     -- Our internal patient ID
  appointment_id: varchar(255), -- Our internal appointment ID  
  ap_id: varchar(255),          -- AP contact/appointment ID
  cc_id: integer,               -- CC patient/appointment ID
  email: varchar(255),
  phone: varchar(255),
  slot: varchar(255),
  patients: jsonb
}
```

### 2. Skip Operations

**For Patient Operations:**
- `hasProcessPatientUpdate(ccId)` - check and delete if exists
- `putProcessPatientUpdate(ccId)` - create record after AP API call
- `hasProcessPatientCreate(apId)` - check and delete if exists  
- `putProcessPatientCreate(apId)` - create record after CC API call

**For Appointment Operations:**
- `hasProcessAppointmentUpdate(ccId)` - check and delete if exists
- `putProcessAppointmentUpdate(ccId)` - create record after AP API call
- `hasProcessAppointmentCreate(apId)` - check and delete if exists
- `putProcessAppointmentCreate(apId)` - create record after CC API call

### 3. Implementation Points

**CC Webhook Processing:**
```typescript
// BEFORE processing CC webhook
if (await Skip.hasProcessPatientUpdate(ccPatient.id)) {
  return "We did this update recently, dropping it"
}

// ... process webhook, make AP API call ...

// AFTER successful AP API call
await Skip.putProcessPatientUpdate(ccPatient.id)
```

**AP Webhook Processing:**
```typescript
// BEFORE processing AP webhook  
if (await Skip.hasProcessPatientCreate(apContact.id)) {
  return "We did this creation recently, dropping it"
}

// ... process webhook, make CC API call ...

// AFTER successful CC API call
await Skip.putProcessPatientCreate(apContact.id)
```

### 4. Key Implementation Rules

1. **Check BEFORE processing** - Use `has*()` methods at the start of webhook processing
2. **Record AFTER API calls** - Use `put*()` methods after successful API calls to other platform
3. **One-time use** - `has*()` methods delete the record when found
4. **Simple ID-based** - No complex hashing, just use platform IDs
5. **Action-specific** - Different actions for create vs update operations
6. **Platform-specific IDs** - Use ccId for CC-originated operations, apId for AP-originated operations

### 5. Where to Implement

**Files to modify:**
1. `New/src/processors/process.ts` - Implement Skip methods
2. `New/src/processors/ccWebhook/eventProcessor.ts` - Add skip checks before processing, skip recording after API calls
3. `New/src/processors/apWebhook/eventProcessor.ts` - Add skip checks before processing, skip recording after API calls
4. `New/src/processors/ccWebhook/patientSynchronizer.ts` - Add skip recording after successful AP API calls
5. `New/src/processors/apWebhook/contactSynchronizer.ts` - Add skip recording after successful CC API calls

### 6. No Complex Features Needed

**What we DON'T need:**
- ❌ Complex operation hashing
- ❌ Persistent audit trails  
- ❌ Automatic cleanup jobs
- ❌ Complex metadata tracking
- ❌ Time-based expiration

**What we DO need:**
- ✅ Simple ID-based skip records
- ✅ Check and delete pattern
- ✅ Record after successful API calls
- ✅ Action-specific tracking (create vs update)
- ✅ Platform-specific IDs (ccId vs apId)

This approach mirrors exactly what works in v3Integration but adapted for the new system structure.

---

## Race Condition Problem & Queued Processing Solution

### The Race Condition Issue

Even with the Skip pattern, we have a timing race condition:

**Current Flow:**
1. CC webhook arrives → We start processing
2. We make AP API call → AP contact updated
3. **RACE CONDITION WINDOW** ⚠️
4. We create Skip record in database
5. AP webhook arrives (triggered by our API call)
6. If AP webhook processing starts before step 4 completes → **DUPLICATE PROCESSING**

**The Problem:**
- Webhook processing is synchronous and immediate
- Skip record creation happens AFTER API calls
- Network latency can cause incoming webhooks to arrive before Skip records are saved
- Multiple webhooks can be processed simultaneously

### Proposed Solution: Queued Webhook Processing

**Core Concept:** Decouple webhook receipt from webhook processing to ensure sequential execution.

#### Phase 1: Webhook Receipt (Immediate)
```typescript
// Webhook endpoint receives request
POST /webhooks/cc or /webhooks/ap
1. Validate webhook payload
2. Store in pending_webhook_events table
3. Trigger async processing
4. Return 200 OK immediately (< 100ms response time)
```

#### Phase 2: Async Sequential Processing
```typescript
// Internal processing endpoint
POST /internal/process-pending-webhooks
1. Query ONE pending webhook event (FIFO order)
2. Process it completely (including Skip record creation)
3. Mark event as processed
4. Check for next pending event
5. Repeat until queue is empty
```

### Database Schema for Queued Processing

```sql
CREATE TABLE "pending_webhook_events" (
  "id" varchar(255) PRIMARY KEY,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,
  "processed_at" timestamp NULL,

  -- Webhook identification
  "source" varchar(10) NOT NULL,           -- 'cc' or 'ap'
  "event_type" varchar(50) NOT NULL,       -- 'patient_update', 'contact_create', etc.
  "entity_id" varchar(255) NOT NULL,       -- CC patient ID or AP contact ID
  "webhook_id" varchar(255),               -- Platform's webhook ID if available

  -- Processing status
  "status" varchar(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  "processing_started_at" timestamp NULL,
  "processing_completed_at" timestamp NULL,
  "retry_count" integer DEFAULT 0,
  "max_retries" integer DEFAULT 3,

  -- Webhook data
  "payload" jsonb NOT NULL,                -- Complete webhook payload
  "headers" jsonb,                         -- Webhook headers for verification

  -- Error handling
  "error_message" text NULL,
  "error_details" jsonb NULL,

  -- Indexes for efficient processing
  INDEX("status", "created_at"),           -- For FIFO processing
  INDEX("source", "entity_id"),           -- For duplicate detection
  INDEX("created_at")                      -- For cleanup
);
```

### Processing Flow with Skip Integration

#### Webhook Receipt Handler
```typescript
async function handleWebhook(source: 'cc' | 'ap', payload: any) {
  // 1. Store webhook event
  const pendingEvent = await db.insert(pendingWebhookEvents).values({
    source,
    event_type: determineEventType(payload),
    entity_id: extractEntityId(payload),
    payload,
    status: 'pending'
  });

  // 2. Trigger async processing (non-blocking)
  triggerAsyncProcessing(); // HTTP call or job queue

  // 3. Return immediately
  return { success: true, queued: true };
}
```

#### Sequential Processor
```typescript
async function processNextPendingWebhook() {
  // 1. Get next pending event (FIFO)
  const event = await db.select()
    .from(pendingWebhookEvents)
    .where(eq(pendingWebhookEvents.status, 'pending'))
    .orderBy(pendingWebhookEvents.createdAt)
    .limit(1);

  if (!event) return; // No pending events

  // 2. Mark as processing
  await db.update(pendingWebhookEvents)
    .set({
      status: 'processing',
      processing_started_at: new Date()
    })
    .where(eq(pendingWebhookEvents.id, event.id));

  try {
    // 3. Process with Skip pattern
    if (event.source === 'cc') {
      // Check Skip BEFORE processing
      if (await Skip.hasProcessPatientUpdate(event.entity_id)) {
        // Mark as completed (skipped)
        await markEventCompleted(event.id, 'skipped');
        return;
      }

      // Process webhook
      await processCCWebhook(event.payload);

      // Create Skip record AFTER processing
      await Skip.putProcessPatientUpdate(event.entity_id);
    }

    // 4. Mark as completed
    await markEventCompleted(event.id, 'completed');

  } catch (error) {
    // 5. Handle errors with retry logic
    await handleProcessingError(event.id, error);
  }

  // 6. Process next event
  setImmediate(() => processNextPendingWebhook());
}
```

### Benefits of Queued Processing

1. **Eliminates Race Conditions:**
   - Only one webhook processed at a time
   - Skip records created before next webhook starts
   - Guaranteed sequential execution

2. **Improved Reliability:**
   - Webhooks stored before processing (no data loss)
   - Retry logic for failed processing
   - Error tracking and debugging

3. **Better Performance:**
   - Fast webhook response times (< 100ms)
   - Platforms don't timeout waiting for processing
   - Async processing doesn't block webhook endpoints

4. **Monitoring & Debugging:**
   - Complete audit trail of all webhooks
   - Processing status tracking
   - Error details for failed events

### Critical Issue: Create Events Don't Have Target Platform IDs

**Problem:** For create events, we don't have the target platform ID yet:
- CC creates patient 123 → We don't have AP contact ID yet
- AP creates contact "abc" → We don't have CC patient ID yet

**Current Skip Pattern Limitation:**
```typescript
// This works for updates
Skip.hasProcessPatientUpdate(ccId)  // ✅ We have ccId from webhook
Skip.putProcessPatientUpdate(ccId)  // ✅ We have ccId from webhook

// This DOESN'T work for creates
Skip.hasProcessPatientCreate(apId)  // ❌ We don't have apId yet!
Skip.putProcessPatientCreate(apId)  // ✅ We get apId after API call
```

### Solution: Use Source Platform ID for Create Events

**Modified Skip Pattern for Creates:**

**CC Create Event (EntityWasCreated):**
```typescript
// BEFORE processing CC create webhook
if (await Skip.hasProcessPatientCreateFromCC(ccPatient.id)) {
  return "We created this patient in CC recently, dropping webhook"
}

// ... process webhook, create in AP ...

// AFTER successful AP API call
await Skip.putProcessPatientCreateFromCC(ccPatient.id)
```

**AP Create Event (contact_created):**
```typescript
// BEFORE processing AP create webhook
if (await Skip.hasProcessContactCreateFromAP(apContact.id)) {
  return "We created this contact in AP recently, dropping webhook"
}

// ... process webhook, create in CC ...

// AFTER successful CC API call
await Skip.putProcessContactCreateFromAP(apContact.id)
```

### Updated Skip Methods for Create Events

```typescript
// For CC-originated creates (we create in AP)
Skip.hasProcessPatientCreateFromCC(ccId)  // Check using source CC ID
Skip.putProcessPatientCreateFromCC(ccId)  // Record using source CC ID

// For AP-originated creates (we create in CC)
Skip.hasProcessContactCreateFromAP(apId)  // Check using source AP ID
Skip.putProcessContactCreateFromAP(apId)  // Record using source AP ID
```

### Updated Database Schema

```sql
-- Add action field to distinguish create directions
CREATE TABLE "skip" (
  "id" varchar(255) PRIMARY KEY,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,

  -- Action identifies the operation and direction
  "action" varchar(50) NOT NULL,           -- 'patient_update', 'patient_create_from_cc', 'contact_create_from_ap'

  -- Entity IDs (only one will be populated based on action)
  "cc_id" integer NULL,                    -- CC patient/appointment ID
  "ap_id" varchar(255) NULL,               -- AP contact/appointment ID

  -- Additional fields for complex scenarios
  "patient_id" varchar(255),               -- Our internal patient ID
  "appointment_id" varchar(255),           -- Our internal appointment ID
  "email" varchar(255),                    -- For email-based matching
  "phone" varchar(255),                    -- For phone-based matching

  INDEX("action", "cc_id"),
  INDEX("action", "ap_id")
);
```

### Complete Skip Actions Map

| Webhook Source | Event Type | Skip Action | ID Used | Purpose |
|---------------|------------|-------------|---------|---------|
| CC | EntityWasUpdated (patient) | `patient_update` | `cc_id` | Prevent CC update loops |
| CC | EntityWasCreated (patient) | `patient_create_from_cc` | `cc_id` | Prevent CC create loops |
| AP | contact_updated | `contact_update` | `ap_id` | Prevent AP update loops |
| AP | contact_created | `contact_create_from_ap` | `ap_id` | Prevent AP create loops |

### Updated Processing Logic

**CC Create Webhook:**
```typescript
async function processCCCreateWebhook(ccPatient) {
  // Check if we recently created this patient in CC
  if (await Skip.hasProcessPatientCreateFromCC(ccPatient.id)) {
    return "We created this patient in CC, dropping webhook";
  }

  // Process: Create contact in AP
  const apContact = await createContactInAP(ccPatient);

  // Record that we created this patient (using CC ID)
  await Skip.putProcessPatientCreateFromCC(ccPatient.id);
}
```

**AP Create Webhook:**
```typescript
async function processAPCreateWebhook(apContact) {
  // Check if we recently created this contact in AP
  if (await Skip.hasProcessContactCreateFromAP(apContact.id)) {
    return "We created this contact in AP, dropping webhook";
  }

  // Process: Create patient in CC
  const ccPatient = await createPatientInCC(apContact);

  // Record that we created this contact (using AP ID)
  await Skip.putProcessContactCreateFromAP(apContact.id);
}
```

### Implementation Considerations

#### Option A: Simple Database Queue (Recommended)
- Use the `pending_webhook_events` table
- Internal HTTP endpoint for processing trigger
- Simple and reliable
- Easy to monitor and debug

#### Option B: Proper Job Queue (Redis/Bull)
- More complex but more robust
- Built-in retry and failure handling
- Better for high-volume scenarios
- Requires additional infrastructure

#### Processing Trigger Options

**Option 1: HTTP Call (Recommended for simplicity)**
```typescript
// After storing webhook
fetch('/internal/process-pending-webhooks', { method: 'POST' });
```

**Option 2: Job Queue**
```typescript
// After storing webhook
await queue.add('process-pending-webhooks', {});
```

#### Error Handling Strategy

1. **Retry Logic:** 3 attempts with exponential backoff
2. **Dead Letter Queue:** Failed events after max retries
3. **Alerting:** Notify on processing failures
4. **Manual Recovery:** Admin interface to reprocess failed events

#### Cleanup Strategy

- Remove completed events older than 7 days
- Keep failed events for debugging
- Archive old events for audit purposes

### Migration Plan

1. **Phase 1:** Implement pending_webhook_events table
2. **Phase 2:** Update webhook endpoints to store events
3. **Phase 3:** Implement sequential processor
4. **Phase 4:** Add monitoring and error handling
5. **Phase 5:** Test thoroughly and deploy
6. **Phase 6:** Monitor and optimize

This queued approach combined with the Skip pattern provides a robust solution for preventing both race conditions and duplicate processing in our bidirectional sync system.
