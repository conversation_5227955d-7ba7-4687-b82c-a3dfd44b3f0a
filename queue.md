# Queued Webhook Processing Implementation Plan

## Current Codebase Analysis

### Existing Structure
```
New/src/
├── handlers/
│   ├── ccWebhookHandler.ts          # CC webhook HTTP handler
│   ├── apContactWebhookHandler.ts   # AP webhook HTTP handler
│   └── apHandler.ts                 # Basic AP handler (placeholder)
├── processors/
│   ├── ccWebhook/
│   │   ├── eventProcessor.ts        # CC event processing logic
│   │   ├── patientSynchronizer.ts   # CC patient sync + checkSyncBuffer()
│   │   ├── fieldMapper.ts           # CC→AP field mapping
│   │   └── types.ts                 # CC webhook types
│   ├── apWebhook/
│   │   ├── eventProcessor.ts        # AP event processing logic
│   │   ├── contactSynchronizer.ts   # AP contact sync + checkSyncBuffer()
│   │   └── types.ts                 # AP webhook types
│   └── process.ts                   # Empty Skip implementation
└── database/
    └── schema.ts                    # Database schema with skip table
```

### Current Sync Buffer Implementation (TO BE REPLACED)
- **CC Webhooks:** `checkSyncBuffer(webhookUpdatedAt, dbCcUpdatedAt, bufferTimeSec)`
- **AP Webhooks:** `checkSyncBuffer(webhookUpdatedAt, dbApUpdatedAt, bufferTimeSec)`
- **Problem:** Time-based comparison with 60-second buffer
- **Issues:** Race conditions, unreliable timing, no operation tracking

### Current Processing Flow (SYNCHRONOUS)
1. Webhook arrives → Handler processes immediately
2. Sync buffer check → Skip if within time window
3. Process webhook → Make API call to other platform
4. Return response → Platform receives result

**Race Condition:** API call triggers immediate webhook before processing completes.

## Implementation Plan

### Phase 1: Database Schema for Queue System

#### New Table: `pending_webhook_events`
```sql
CREATE TABLE "pending_webhook_events" (
  "id" varchar(255) PRIMARY KEY NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,
  
  -- Event identification
  "source" varchar(10) NOT NULL,           -- 'cc' or 'ap'
  "event_type" varchar(50) NOT NULL,       -- 'patient_created', 'patient_updated', 'contact_created'
  "entity_id" varchar(255) NOT NULL,       -- CC patient ID or AP contact ID
  "webhook_id" varchar(255),               -- Platform webhook ID if available
  
  -- Processing status
  "status" varchar(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  "processing_started_at" timestamp NULL,
  "processing_completed_at" timestamp NULL,
  "retry_count" integer DEFAULT 0,
  "max_retries" integer DEFAULT 3,
  
  -- Webhook data
  "payload" jsonb NOT NULL,                -- Complete webhook payload
  "headers" jsonb,                         -- Webhook headers
  
  -- Error handling
  "error_message" text NULL,
  "error_details" jsonb NULL,
  
  -- Indexes for efficient processing
  INDEX("status", "created_at"),           -- FIFO processing
  INDEX("source", "entity_id"),           -- Duplicate detection
  INDEX("created_at")                      -- Cleanup
);
```

#### Enhanced Skip Table
```sql
-- Add action field to existing skip table
ALTER TABLE "skip" ADD COLUMN "action" varchar(50);

-- Skip actions for different operations:
-- 'patient_update_cc_to_ap'    - CC patient update processed
-- 'patient_create_cc_to_ap'    - CC patient create processed  
-- 'contact_update_ap_to_cc'    - AP contact update processed
-- 'contact_create_ap_to_cc'    - AP contact create processed
```

### Phase 2: Queue Management System

#### New File: `New/src/processors/webhookQueue.ts`
```typescript
interface QueuedWebhookEvent {
  id: string;
  source: 'cc' | 'ap';
  eventType: string;
  entityId: string;
  payload: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  retryCount: number;
  createdAt: Date;
}

class WebhookQueue {
  // Store webhook event in queue
  async enqueueWebhook(event: QueuedWebhookEvent): Promise<string>
  
  // Get next pending event (FIFO)
  async getNextPendingEvent(): Promise<QueuedWebhookEvent | null>
  
  // Update event status
  async updateEventStatus(id: string, status: string, error?: string): Promise<void>
  
  // Mark event as processing
  async markAsProcessing(id: string): Promise<void>
  
  // Mark event as completed
  async markAsCompleted(id: string): Promise<void>
  
  // Handle failed event with retry logic
  async handleFailedEvent(id: string, error: string): Promise<void>
  
  // Cleanup old completed events
  async cleanupOldEvents(olderThanDays: number = 7): Promise<number>
}
```

### Phase 3: Modified Webhook Handlers (Immediate Response)

#### Updated: `New/src/handlers/ccWebhookHandler.ts`
```typescript
export async function ccWebhookHandler(c: Context): Promise<Response> {
  const startTime = Date.now();
  
  try {
    // 1. Parse and validate webhook payload
    const payload = await parseWebhookPayload(c);
    if (!payload) {
      return c.json({ status: "error", message: "Invalid payload" }, 400);
    }

    // 2. Store in queue (fast operation)
    const eventId = await webhookQueue.enqueueWebhook({
      source: 'cc',
      eventType: `${payload.model.toLowerCase()}_${payload.event.toLowerCase()}`,
      entityId: payload.id.toString(),
      payload,
      status: 'pending',
      retryCount: 0,
      createdAt: new Date()
    });

    // 3. Trigger async processing (non-blocking)
    triggerAsyncProcessing(); // HTTP call to internal endpoint

    // 4. Return immediate success (< 100ms)
    return c.json({
      status: "success",
      message: "Webhook queued for processing",
      eventId,
      queuedAt: new Date().toISOString(),
      processingTime: Date.now() - startTime
    }, 200);

  } catch (error) {
    return c.json({
      status: "error", 
      message: "Failed to queue webhook"
    }, 500);
  }
}
```

#### Updated: `New/src/handlers/apContactWebhookHandler.ts`
```typescript
export async function apContactWebhookHandler(c: Context): Promise<Response> {
  // Same pattern as CC handler but with source: 'ap'
  // Store AP webhook events in same queue table
  // Trigger same async processing endpoint
}
```

### Phase 4: Sequential Webhook Processor

#### New File: `New/src/processors/queueProcessor.ts`
```typescript
class QueueProcessor {
  private isProcessing = false;

  async processNextEvent(): Promise<void> {
    if (this.isProcessing) return; // Prevent concurrent processing
    
    this.isProcessing = true;
    
    try {
      const event = await webhookQueue.getNextPendingEvent();
      if (!event) {
        this.isProcessing = false;
        return; // No pending events
      }

      await webhookQueue.markAsProcessing(event.id);
      
      try {
        // Process based on source
        if (event.source === 'cc') {
          await this.processCCEvent(event);
        } else if (event.source === 'ap') {
          await this.processAPEvent(event);
        }
        
        await webhookQueue.markAsCompleted(event.id);
        
      } catch (error) {
        await webhookQueue.handleFailedEvent(event.id, error.message);
      }
      
    } finally {
      this.isProcessing = false;
    }
    
    // Process next event if available
    setImmediate(() => this.processNextEvent());
  }

  private async processCCEvent(event: QueuedWebhookEvent): Promise<void> {
    const payload = event.payload;
    
    // 1. Check Skip record BEFORE processing
    if (await Skip.hasProcessPatientUpdate(payload.id)) {
      logInfo('Skipping CC event - we processed this recently');
      return;
    }
    
    // 2. Process using existing CC processor
    const result = await processWebhookEvent(payload);
    
    if (!result.success) {
      throw new Error(result.error?.message || 'CC processing failed');
    }
    
    // 3. Create Skip record AFTER successful processing
    await Skip.putProcessPatientUpdate(payload.id);
  }

  private async processAPEvent(event: QueuedWebhookEvent): Promise<void> {
    // Similar pattern for AP events
    // Check Skip → Process → Create Skip record
  }
}
```

### Phase 5: Skip Pattern Integration

#### Updated: `New/src/processors/process.ts`
```typescript
class Process {
  // Patient operations
  async hasProcessPatientUpdate(ccId: number): Promise<boolean> {
    return this.checkAndDeleteSkip('patient_update_cc_to_ap', ccId.toString(), 'cc_id');
  }
  
  async putProcessPatientUpdate(ccId: number): Promise<void> {
    await this.createSkip('patient_update_cc_to_ap', ccId.toString(), 'cc_id');
  }
  
  async hasProcessPatientCreate(ccId: number): Promise<boolean> {
    return this.checkAndDeleteSkip('patient_create_cc_to_ap', ccId.toString(), 'cc_id');
  }
  
  async putProcessPatientCreate(ccId: number): Promise<void> {
    await this.createSkip('patient_create_cc_to_ap', ccId.toString(), 'cc_id');
  }

  // Contact operations  
  async hasProcessContactUpdate(apId: string): Promise<boolean> {
    return this.checkAndDeleteSkip('contact_update_ap_to_cc', apId, 'ap_id');
  }
  
  async putProcessContactUpdate(apId: string): Promise<void> {
    await this.createSkip('contact_update_ap_to_cc', apId, 'ap_id');
  }
  
  async hasProcessContactCreate(apId: string): Promise<boolean> {
    return this.checkAndDeleteSkip('contact_create_ap_to_cc', apId, 'ap_id');
  }
  
  async putProcessContactCreate(apId: string): Promise<void> {
    await this.createSkip('contact_create_ap_to_cc', apId, 'ap_id');
  }

  // Core Skip operations
  private async checkAndDeleteSkip(action: string, entityId: string, idField: string): Promise<boolean> {
    const existing = await db.select().from(skip)
      .where(and(eq(skip.action, action), eq(skip[idField], entityId)));
    
    if (existing.length > 0) {
      await db.delete(skip).where(eq(skip.id, existing[0].id));
      return true; // Found and deleted
    }
    
    return false; // Not found
  }
  
  private async createSkip(action: string, entityId: string, idField: string): Promise<void> {
    await db.insert(skip).values({
      action,
      [idField]: entityId,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }
}
```

### Phase 6: Internal Processing Endpoint

#### New File: `New/src/handlers/internalHandler.ts`
```typescript
export async function processQueuedWebhooks(c: Context): Promise<Response> {
  try {
    // Trigger queue processing (non-blocking)
    const processor = new QueueProcessor();
    processor.processNextEvent(); // Fire and forget

    return c.json({
      status: "success",
      message: "Queue processing triggered",
      timestamp: new Date().toISOString()
    }, 200);

  } catch (error) {
    return c.json({
      status: "error",
      message: "Failed to trigger queue processing"
    }, 500);
  }
}
```

### Phase 7: Benefits of Queued System

#### Race Condition Resolution
**Before (Synchronous):**
```
CC Webhook → Process → API Call → Skip Record
                ↓
            AP Webhook arrives BEFORE Skip record created
                ↓
            DUPLICATE PROCESSING
```

**After (Queued):**
```
CC Webhook → Queue → Return 200 OK
                ↓
            Sequential Processing:
            1. Process CC webhook → API call → Skip record
            2. Process AP webhook → Check Skip → SKIPPED
```

#### ID Availability for Create Events
**Sequential processing solves the create event ID problem:**

1. **CC creates patient 123** → Webhook queued
2. **Process CC create** → Create AP contact "xyz" → Skip record with CC ID 123
3. **AP webhook for contact "xyz"** → Queued after CC processing completes
4. **Process AP webhook** → Check Skip with AP ID "xyz" → SKIPPED

**Key insight:** By the time we process the AP webhook, we have both CC ID (123) and AP ID ("xyz") available.

### Phase 8: Migration Strategy

#### Step 1: Deploy Queue Infrastructure
- Add `pending_webhook_events` table
- Add `action` column to `skip` table
- Deploy queue management classes

#### Step 2: Update Webhook Handlers (Parallel Processing)
- Modify handlers to store events in queue AND process synchronously
- Monitor both paths for consistency
- Gradual rollout with feature flags

#### Step 3: Switch to Queue-Only Processing
- Remove synchronous processing from handlers
- All webhooks go through queue system
- Monitor performance and error rates

#### Step 4: Remove Legacy Sync Buffer
- Remove `checkSyncBuffer()` calls
- Remove time-based buffer configuration
- Clean up legacy code

### Phase 9: Error Handling & Monitoring

#### Retry Logic
- 3 retry attempts with exponential backoff
- Dead letter queue for failed events after max retries
- Manual recovery interface for failed events

#### Monitoring
- Queue depth monitoring
- Processing time metrics
- Error rate tracking
- Skip record effectiveness metrics

#### Alerting
- Queue backup alerts (> 100 pending events)
- High error rate alerts (> 5% failure rate)
- Processing delay alerts (events older than 5 minutes)

### Phase 10: Testing Strategy

#### Unit Tests
- Queue operations (enqueue, dequeue, status updates)
- Skip pattern operations (check and delete, create)
- Event processing logic

#### Integration Tests
- End-to-end webhook processing
- Race condition scenarios
- Error handling and retry logic

#### Load Testing
- High-volume webhook scenarios
- Concurrent webhook processing
- Queue performance under load

### Phase 11: Performance Considerations

#### Database Optimization
- Proper indexing on queue table
- Regular cleanup of old events
- Connection pooling for high throughput

#### Memory Management
- Limit concurrent processing to prevent memory issues
- Efficient JSON handling for large payloads
- Garbage collection optimization

#### Scalability
- Horizontal scaling with multiple queue processors
- Database sharding for very high volumes
- Redis-based queue for extreme scale (future)

## Implementation Timeline

**Week 1:** Database schema + Queue management classes
**Week 2:** Updated webhook handlers + Internal processing endpoint
**Week 3:** Skip pattern integration + Sequential processor
**Week 4:** Testing + Monitoring + Error handling
**Week 5:** Deployment + Migration from sync buffer
**Week 6:** Performance optimization + Documentation

This queued system eliminates race conditions, provides reliable loop prevention, and solves the ID availability problem for create events through sequential processing.
```
